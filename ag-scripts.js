//Fix Double Tap on IOS 

jQuery('a').on('touchend', function() {
    jQuery(this).click();
});





jQuery(document).ready(function($) {
    let page = 1;
    $('#ags-load-more').on('click', function() {
        page++;
        $.ajax({
            url: ags_ajax.ajaxurl,
            type: 'POST',
            data: {
                action: 'ags_load_more_posts',
                nonce: ags_ajax.nonce,
                page: page,
                postType: ags_ajax.postType,
                postsPerPage: ags_ajax.postsPerPage,
            },
            success: function(response) {
                $('#ags-posts-list').append(response);
            },
            error: function() {
                console.log('Error loading posts.');
            }
        });
    });
});