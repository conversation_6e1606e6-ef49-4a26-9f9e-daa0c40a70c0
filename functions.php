<?php
/**
 * @version    1.1
 * @package    Kadence Ag Starter Theme
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (C) 2023 Abdullah.G All Rights Reserved.
 * @license    Rights Reserved to <PERSON> <<EMAIL>> .
 */

add_action('wp_enqueue_scripts', 'ag_child_enqueue_styles', 10000);
function ag_child_enqueue_styles() {
	wp_enqueue_style( 'ag-css', get_stylesheet_directory_uri() . '/style.css' );
	wp_enqueue_script(
        'ag-js',
        get_stylesheet_directory_uri() . '/ag-scripts.js',
        array( 'jquery' )
    );
}


add_action('wp_head', 'load_arabic_style');
function load_arabic_style() {
    if ( is_rtl() ) {
        $css_file_path = get_stylesheet_directory() . '/style-rtl.css';  // Child theme directory
        if (file_exists($css_file_path)) {
            echo '<style type="text/css">' . file_get_contents($css_file_path) . '</style>';
        }
    }
}



function modify_kadence_svg_icon($svg, $icon, $icon_title) {
    // Target specifically the menu SVG
    if ($icon === 'menu' || strpos($svg, 'kadence-menu-svg') !== false) {
        // Replace with your custom SVG
        return '<svg width="38px" height="38px" viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="4.608"></g><g id="SVGRepo_iconCarrier"> <path d="M7.28308 19L20 19M20 5L12.9719 5" stroke="#ffffff" stroke-width="1.8" stroke-linecap="round"></path> <path d="M19.9996 12L4 12" stroke="#62E587" stroke-width="1.8" stroke-linecap="round"></path> </g></svg>';
    }
    return $svg;
}
add_filter('kadence_svg_icon', 'modify_kadence_svg_icon', 10, 3);







require_once get_stylesheet_directory() . '/inc/post-types.php';
require_once get_stylesheet_directory() . '/inc/ags-helper.php';
require_once get_stylesheet_directory() . '/inc/option-config.php';
