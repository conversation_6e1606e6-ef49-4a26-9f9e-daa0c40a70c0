<?php
/**
 * @version    1.1
 * @package    Kadence Ag Starter Theme
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (C) 2023 Abdullah.G All Rights Reserved.
 * @license    Rights Reserved to <PERSON> <<EMAIL>> .
 */


require_once get_stylesheet_directory() . '/inc/tgmpa/class-tgm-plugin-activation.php';

add_action( 'tgmpa_register', 'ag_register_required_plugins' );


function ag_register_required_plugins() {

	$plugins = array(

		array(
            'name'      => esc_html__( 'CMB2 Framework', 'couponko' ),
			'slug'      => 'cmb2',
			'required'  => true,
		),


	);

	$config = array(
		'id'           => 'ag-themestart',         // Unique ID for hashing notices for multiple instances of TGMPA.
		'default_path' => '',                      // Default absolute path to bundled plugins.
		'menu'         => 'tgmpa-install-plugins', // Menu slug.
		'has_notices'  => true,                    // Show admin notices or not.
		'dismissable'  => false,                    // If false, a user cannot dismiss the nag message.
		'dismiss_msg'  => '',                      // If 'dismissable' is false, this message will be output at top of nag.
		'is_automatic' => true,                   // Automatically activate plugins after installation or not.
		'message'      => 'لكي يعمل القالب بشكل صحيح لابد من تثبيت الاضافات التالية',                      // Message to output right before the plugins table.

		
		'strings'      => array(
			'page_title'                      => __( 'Install Required Plugins', 'kadence' ),
			'menu_title'                      => __( 'Install Plugins', 'kadence' ),
			'installing'                      => __( 'Installing Plugin: %s', 'kadence' ),
			'updating'                        => __( 'Updating Plugin: %s', 'kadence' ),
			'oops'                            => __( 'Something went wrong with the plugin API.', 'kadence' ),
			'notice_can_install_required'     => _n_noop(
				'لكي يعمل القالب بشكل صحيح لابد من تثبيت الاضافات التالية: %1$s.',
				'This theme requires the following plugins: %1$s.',
				'kadence'
			)),

	);
	tgmpa( $plugins, $config );
}
