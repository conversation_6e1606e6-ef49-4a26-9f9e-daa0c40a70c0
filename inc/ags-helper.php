<?php
/**
 * Modern Creative Testimonials Slider Shortcode
 * Uses Kadence Blocks Pro Splide Library
 *
 * Usage: [modern_testimonials count="6" autoplay="true" speed="3000"]
 */
function ags_modern_testimonials_shortcode( $atts ) {
    // Parse shortcode attributes
    $atts = shortcode_atts( array(
        'count'     => 6,
        'autoplay'  => 'true',
        'speed'     => '3000',
        'category'  => '',
        'order'     => 'DESC',
        'orderby'   => 'date'
    ), $atts, 'modern_testimonials' );

    // Enqueue Kadence Blocks Pro Splide assets
    $kadence_splide_js = '/wp-content/plugins/kadence-blocks-pro/includes/assets/js/splide.min.js';
    $kadence_splide_css = '/wp-content/plugins/kadence-blocks-pro/includes/assets/css/kadence-splide.min.css';

    // Check if Kadence Blocks Pro Splide files exist
    if ( file_exists( ABSPATH . $kadence_splide_js ) && file_exists( ABSPATH . $kadence_splide_css ) ) {
        wp_enqueue_style( 'kadence-splide', $kadence_splide_css, array(), '1.0.0' );
        wp_enqueue_script( 'kadence-splide', $kadence_splide_js, array(), '1.0.0', true );
    } else {
        // Fallback to CDN if Kadence files not found
        wp_enqueue_style( 'splide-css', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css', array(), '4.1.4' );
        wp_enqueue_script( 'splide-js', 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js', array(), '4.1.4', true );
    }

    // Query testimonials
    $testimonials_query = new WP_Query( array(
        'post_type'      => 'testimonials',
        'posts_per_page' => intval( $atts['count'] ),
        'post_status'    => 'publish',
        'orderby'        => $atts['orderby'],
        'order'          => $atts['order']
    ) );

    // Return early if no testimonials found
    if ( ! $testimonials_query->have_posts() ) {
        return '<div class="no-testimonials">No testimonials found.</div>';
    }

    // Start output buffering
    ob_start();

    // Generate unique slider ID
    $slider_id = 'modern-testimonials-' . uniqid();

    // Default avatar for testimonials without images
    $default_avatar = 'data:image/svg+xml;base64,' . base64_encode('
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="50" fill="#e9ecef"/>
            <circle cx="50" cy="35" r="15" fill="#6c757d"/>
            <path d="M20 80 Q20 65 35 65 L65 65 Q80 65 80 80 Z" fill="#6c757d"/>
        </svg>
    ');
    ?>

    <style>
        /* MODERN CREATIVE TESTIMONIALS - KADENCE STRUCTURE */

        /* Main Wrapper - Following Kadence Pattern */
        .kt-blocks-testimonials-wrap-modern {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
			padding-block-start:60px;
        }

        /* Inner Wrapper with Modern Background */
        .kt-blocks-testimonials-inner-wrap-modern {
            position: relative;
            border-radius: 20px;
            overflow: visible;
        }

        /* Background Pattern */
        .kt-blocks-testimonials-inner-wrap-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        /* Kadence Carousel Container */
        .kt-blocks-carousel-init-modern {
            position: relative;
            z-index: 2;
        }

        /* Splide Structure - Following Kadence Pattern */
        #<?php echo $slider_id; ?>.kt-blocks-carousel-init-modern.kb-gallery-carousel.kt-carousel-arrowstyle-whiteondark.kt-carousel-dotstyle-none.kb-splide.splide {
            position: relative;
            overflow: visible;
        }

        #<?php echo $slider_id; ?> .splide__track {
            position: relative;
            overflow: visible;
        }

        #<?php echo $slider_id; ?> .splide__list {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        /* Slide Items - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-blocks-testimonial-carousel-item.kb-slide-item.splide__slide {
            flex: 0 0 auto;
            display: block;
            position: relative;
            margin-right: 30px;
        }

        /* Testimonial Item Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-item-wrap {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            height: 100%;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #<?php echo $slider_id; ?> .kt-testimonial-item-wrap:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 1);
        }

        /* Testimonial Text Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-text-wrap {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Quote Icon - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-svg-testimonial-global-icon-wrap {
            position: absolute;
            top: -70px!important;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #08074b, #08074b)!important;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgb(8 7 75 / 30%);
            z-index: 3;
        }

        #<?php echo $slider_id; ?> .kt-svg-testimonial-global-icon svg {
            width: 24px;
            height: 24px;
            fill: #63e687;
        }

            /* Avatar */
            #<?php echo $slider_id; ?> .testimonial-avatar {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                margin: 30px auto 20px;
                position: relative;
                overflow: hidden;
                border: 4px solid rgba(255, 255, 255, 0.8);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            #<?php echo $slider_id; ?> .testimonial-avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            #<?php echo $slider_id; ?> .testimonial-card:hover .testimonial-avatar img {
                transform: scale(1.1);
            }

        /* Content Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-content-wrap {
            flex-grow: 1;
            margin: 30px 0 20px 0;
            position: relative;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-content {
            font-size: 16px;
            line-height: 1.6;
            color: #444;
            font-style: italic;
            position: relative;
            text-align: center;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-content::before,
        #<?php echo $slider_id; ?> .kt-testimonial-content::after {
            content: '"';
            font-size: 40px;
            color: #ddd;
            position: absolute;
            font-family: Georgia, serif;
            line-height: 1;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-content::before {
            top: -10px;
            left: -10px;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-content::after {
            bottom: -20px;
            right: -10px;
            transform: rotate(180deg);
        }

            /* Rating Stars */
            #<?php echo $slider_id; ?> .testimonial-rating {
                margin: 15px 0;
                display: flex;
                justify-content: center;
                gap: 3px;
            }

            #<?php echo $slider_id; ?> .star {
                color: #ffd700;
                font-size: 18px;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                transition: transform 0.2s ease;
            }

            #<?php echo $slider_id; ?> .testimonial-card:hover .star {
                transform: scale(1.1);
            }

        /* Meta Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-meta-wrap {
            margin-top: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding-top: 20px;
        }

        /* Media Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-media-wrap {
            flex-shrink: 0;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-media-inner-wrap {
            position: relative;
        }

        #<?php echo $slider_id; ?> .kadence-testimonial-image-intrisic {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        #<?php echo $slider_id; ?> .kt-testimonial-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-item-wrap:hover .kt-testimonial-image {
            transform: scale(1.1);
        }

        /* Meta Name Wrap - Kadence Structure */
        #<?php echo $slider_id; ?> .kt-testimonial-meta-name-wrap {
            text-align: left;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-name-wrap .kt-testimonial-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
			color: var(--global-palette1);
            line-height: 1.2;
        }

        #<?php echo $slider_id; ?> .kt-testimonial-occupation-wrap .kt-testimonial-occupation {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            margin: 0;
        }

            /* Navigation Arrows */
            #<?php echo $slider_id; ?> .splide__arrow {
                background: rgba(255, 255, 255, 0.9);
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                opacity: 0.8;
                z-index: 10;
            }

            #<?php echo $slider_id; ?> .splide__arrow:hover {
                background: rgba(255, 255, 255, 1);
                transform: scale(1.1);
                opacity: 1;
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
            }

            #<?php echo $slider_id; ?> .splide__arrow svg {
                fill: #667eea;
                width: 20px;
                height: 20px;
                transition: fill 0.3s ease;
            }

            #<?php echo $slider_id; ?> .splide__arrow:hover svg {
                fill: #764ba2;
            }

            #<?php echo $slider_id; ?> .splide__arrow--prev {
                left: -25px;
            }

            #<?php echo $slider_id; ?> .splide__arrow--next {
                right: -25px;
            }

            /* Pagination Dots */
            #<?php echo $slider_id; ?> .splide__pagination {
                bottom: -50px;
                text-align: center;
            }

            #<?php echo $slider_id; ?> .splide__pagination__page {
                background: rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                width: 12px;
                height: 12px;
                margin: 0 6px;
                transition: all 0.3s ease;
                border: none;
            }

            #<?php echo $slider_id; ?> .splide__pagination__page.is-active {
                background: rgba(255, 255, 255, 1);
                transform: scale(1.3);
                box-shadow: 0 4px 15px rgba(255, 255, 255, 0.4);
            }

            /* Responsive Design */
            @media (max-width: 1024px) {
                #<?php echo $slider_id; ?> .splide__slide {
                    width: calc(50% - 15px);
                }

                #<?php echo $slider_id; ?> .splide__list {
                    gap: 20px;
                }

                #<?php echo $slider_id; ?> {
                    padding: 50px 30px;
                }
            }

            @media (max-width: 768px) {
                #<?php echo $slider_id; ?> .splide__slide {
                    width: 100%;
                }

                #<?php echo $slider_id; ?> .splide__list {
                    gap: 15px;
                }

                #<?php echo $slider_id; ?> {
                    padding: 40px 20px;
                }

                #<?php echo $slider_id; ?> .testimonial-card {
                    min-height: 350px;
                    padding: 30px 20px;
                }

                #<?php echo $slider_id; ?> .splide__arrow {
                    display: none;
                }

                .modern-testimonials-wrapper {
                    padding: 40px 15px;
                }
            }

            @media (max-width: 480px) {
                #<?php echo $slider_id; ?> .testimonial-card {
                    min-height: 320px;
                    padding: 25px 15px;
                }

                #<?php echo $slider_id; ?> .testimonial-content {
                    font-size: 14px;
                }

                #<?php echo $slider_id; ?> .quote-icon {
                    width: 50px;
                    height: 50px;
                    top: -12px;
                }

                #<?php echo $slider_id; ?> .quote-icon svg {
                    width: 20px;
                    height: 20px;
                }

                #<?php echo $slider_id; ?> .testimonial-avatar {
                    width: 60px;
                    height: 60px;
                }
            }
        </style>

    <!-- Modern Testimonials - Kadence Structure -->
    <div class="kt-blocks-testimonials-wrap-modern kt-testimonial-halign-center kt-testimonial-style-bubble kt-testimonials-media-on kt-testimonials-icon-on kt-testimonial-columns-3 kt-t-xxl-col-3 kt-t-xl-col-3 kt-t-lg-col-3 kt-t-md-col-2 kt-t-sm-col-1 kt-t-xs-col-1 wp-block-kadence-testimonials">
        <div class="kt-blocks-testimonials-inner-wrap-modern kt-blocks-carousel kt-carousel-container-dotstyle-none kt-carousel-container-arrowstyle-whiteondark">
            <div id="<?php echo $slider_id; ?>" class="kt-blocks-carousel-init-modern kb-gallery-carousel kt-carousel-arrowstyle-whiteondark kt-carousel-dotstyle-none kb-splide splide"
                 data-columns-xxl="3"
                 data-columns-xl="3"
                 data-columns-md="3"
                 data-columns-sm="2"
                 data-columns-xs="1"
                 data-columns-ss="1"
                 data-slider-anim-speed="400"
                 data-slider-scroll="1"
                 data-slider-type="loop"
                 data-slider-arrows="true"
                 data-slider-dots="false"
                 data-slider-hover-pause="false"
                 data-slider-auto="<?php echo $atts['autoplay'] === 'true' ? '1' : '0'; ?>"
                 data-slider-speed="<?php echo intval( $atts['speed'] ); ?>"
                 data-slider-gap="30px"
                 data-slider-gap-tablet="30px"
                 data-slider-gap-mobile="30px"
                 role="region"
                 aria-roledescription="carousel">

                <div class="splide__track">
                    <div class="splide__list" role="presentation">
                    <?php while ( $testimonials_query->have_posts() ) : $testimonials_query->the_post(); ?>
                        <?php
                        // Get testimonial meta data
                        $job_title = get_post_meta( get_the_ID(), '_testimonial_job_title', true );
                        $company = get_post_meta( get_the_ID(), '_testimonial_company', true );
                        $rating = get_post_meta( get_the_ID(), '_testimonial_rating', true );

                        // Get featured image or use default avatar
                        $image_url = get_the_post_thumbnail_url( get_the_ID(), 'medium' );
                        if ( ! $image_url ) {
                            $image_url = $default_avatar;
                        }

                        // Default rating if not set
                        if ( empty( $rating ) || ! is_numeric( $rating ) ) {
                            $rating = 5;
                        }
                        $rating = max( 1, min( 5, intval( $rating ) ) );
                        ?>
                        <div class="kt-blocks-testimonial-carousel-item kb-slide-item splide__slide"
                             role="group"
                             aria-roledescription="slide"
                             style="margin-right: 30px;">

                            <div class="kt-testimonial-item-wrap kt-testimonial-item-modern-<?php echo get_the_ID(); ?> wp-block-kadence-testimonial">

                                <!-- Text Wrap -->
                                <div class="kt-testimonial-text-wrap">
                                    <!-- Quote Icon -->
                                    <div class="kt-svg-testimonial-global-icon-wrap">
                                        <div class="kt-svg-testimonial-global-icon kt-svg-testimonial-global-icon-icon-ic_quoteSerifLeft">
                                            <svg viewBox="0 0 8 8" fill="currentColor" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                <path d="M3 0c-1.65 0-3 1.35-3 3v3h3v-3h-2c0-1.11.89-2 2-2v-1zm5 0c-1.65 0-3 1.35-3 3v3h3v-3h-2c0-1.11.89-2 2-2v-1z" transform="translate(0 1)"></path>
                                            </svg>
                                        </div>
                                    </div>

                                    <!-- Content -->
                                    <div class="kt-testimonial-content-wrap">
                                        <div class="kt-testimonial-content">
                                            <?php
                                            $content = get_the_content();
                                            $content = wp_strip_all_tags( $content );
                                            $content = wp_trim_words( $content, 25, '...' );
                                            echo esc_html( $content );
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Meta Wrap -->
                                <div class="kt-testimonial-meta-wrap">
                                    <!-- Media Wrap -->
                                    <div class="kt-testimonial-media-wrap">
                                        <div class="kt-testimonial-media-inner-wrap">
                                            <div class="kadence-testimonial-image-intrisic">
                                                <img decoding="async"
                                                     src="<?php echo esc_url( $image_url ); ?>"
                                                     class="kt-testimonial-image"
                                                     alt="<?php echo esc_attr( get_the_title() ); ?>"
                                                     loading="lazy">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Meta Name Wrap -->
                                    <div class="kt-testimonial-meta-name-wrap">
                                        <div class="kt-testimonial-name-wrap">
                                            <div class="kt-testimonial-name"><?php the_title(); ?></div>
                                        </div>
                                        <div class="kt-testimonial-occupation-wrap">
                                            <div class="kt-testimonial-occupation">
                                                <?php
                                                if ( $job_title && $company ) {
                                                    echo esc_html( $job_title . ' of ' . $company );
                                                } elseif ( $job_title ) {
                                                    echo esc_html( $job_title );
                                                } elseif ( $company ) {
                                                    echo esc_html( $company );
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Modern Testimonials Slider - Kadence Style Initialization
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize Kadence-style testimonials slider
            setTimeout(function() {
                if (typeof Splide !== 'undefined') {
                    console.log('✅ Splide library found, initializing modern testimonials...');

                    var slider = document.getElementById('<?php echo $slider_id; ?>');
                    if (!slider) {
                        console.error('❌ Slider element not found');
                        return;
                    }

                    // Kadence-style configuration
                    var splideConfig = {
                        type: slider.getAttribute('data-slider-type') || 'loop',
                        perPage: parseInt(slider.getAttribute('data-columns-xxl')) || 3,
                        perMove: parseInt(slider.getAttribute('data-slider-scroll')) || 1,
                        gap: slider.getAttribute('data-slider-gap') || '30px',
                        autoplay: slider.getAttribute('data-slider-auto') === '1',
                        interval: parseInt(slider.getAttribute('data-slider-speed')) || 3000,
                        speed: parseInt(slider.getAttribute('data-slider-anim-speed')) || 400,
                        pauseOnHover: slider.getAttribute('data-slider-hover-pause') !== 'false',
                        pauseOnFocus: true,
                        arrows: slider.getAttribute('data-slider-arrows') === 'true',
                        pagination: slider.getAttribute('data-slider-dots') === 'true',
                        drag: true,
                        keyboard: true,
                        wheel: false,
                        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                        breakpoints: {
                            1200: {
                                perPage: parseInt(slider.getAttribute('data-columns-xl')) || 3,
                                gap: slider.getAttribute('data-slider-gap-tablet') || '30px',
                            },
                            1024: {
                                perPage: parseInt(slider.getAttribute('data-columns-md')) || 3,
                                gap: slider.getAttribute('data-slider-gap-tablet') || '30px',
                            },
                            768: {
                                perPage: parseInt(slider.getAttribute('data-columns-sm')) || 2,
                                gap: slider.getAttribute('data-slider-gap-mobile') || '30px',
                            },
                            576: {
                                perPage: parseInt(slider.getAttribute('data-columns-xs')) || 1,
                                gap: slider.getAttribute('data-slider-gap-mobile') || '30px',
                                arrows: false,
                            }
                        }
                    };

                    try {
                        var modernTestimonials = new Splide( '#<?php echo $slider_id; ?>', splideConfig );

                        modernTestimonials.on( 'mounted', function () {
                            console.log( '✅ Modern Testimonials slider mounted successfully (Kadence structure)' );

                            // Ensure equal height for all cards - Kadence style
                            var cards = document.querySelectorAll( '#<?php echo $slider_id; ?> .kt-testimonial-item-wrap' );
                            var maxHeight = 0;

                            cards.forEach( function( card ) {
                                card.style.height = 'auto';
                                var height = card.offsetHeight;
                                if ( height > maxHeight ) {
                                    maxHeight = height;
                                }
                            });

                            cards.forEach( function( card ) {
                                card.style.height = maxHeight + 'px';
                            });

                            // Add Kadence-style loaded class
                            slider.classList.add('kt-carousel-loaded');
                        });

                        modernTestimonials.mount();

                    } catch (error) {
                        console.error('❌ Error initializing modern testimonials slider:', error);

                        // Fallback to static grid layout
                        var container = document.getElementById('<?php echo $slider_id; ?>');
                        if ( container ) {
                            container.style.display = 'block';
                            var track = container.querySelector('.splide__track');
                            var list = container.querySelector('.splide__list');

                            if (track && list) {
                                track.style.width = '100%';
                                list.style.display = 'grid';
                                list.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
                                list.style.gap = '30px';
                                list.style.transform = 'none';
                            }
                        }
                    }
                } else {
                    console.warn('⚠️ Splide library not found. Loading from CDN...');

                    // Load Splide from CDN as fallback
                    var splideCSS = document.createElement('link');
                    splideCSS.rel = 'stylesheet';
                    splideCSS.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
                    document.head.appendChild(splideCSS);

                    var splideJS = document.createElement('script');
                    splideJS.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
                    splideJS.onload = function() {
                        // Retry initialization after CDN load
                        setTimeout(function() {
                            if (typeof Splide !== 'undefined') {
                                var modernTestimonials = new Splide( '#<?php echo $slider_id; ?>', splideConfig );
                                modernTestimonials.mount();
                                console.log( '✅ Modern Testimonials loaded from CDN and mounted' );
                            }
                        }, 100);
                    };
                    document.head.appendChild(splideJS);
                }
            }, 100);
        });
    </script>

    <?php
    // Reset post data
    wp_reset_postdata();

    // Return the output
    return ob_get_clean();
}

// Register the modern testimonials shortcode
add_shortcode( 'testimonials_slider', 'ags_modern_testimonials_shortcode' );