<?php
/**
 * Custom Post Types and Taxonomies
 */

// Register Services Custom Post Type
function register_services_post_type() {
	$labels = array(
		'name'               => _x( 'Services', 'post type general name', '100mints' ),
		'singular_name'      => _x( 'Service', 'post type singular name', '100mints' ),
		'menu_name'          => _x( 'Services', 'admin menu', '100mints' ),
		'name_admin_bar'     => _x( 'Service', 'add new on admin bar', '100mints' ),
		'add_new'            => _x( 'Add New', 'service', '100mints' ),
		'add_new_item'       => __( 'Add New Service', '100mints' ),
		'new_item'           => __( 'New Service', '100mints' ),
		'edit_item'          => __( 'Edit Service', '100mints' ),
		'view_item'          => __( 'View Service', '100mints' ),
		'all_items'          => __( 'All Services', '100mints' ),
		'search_items'       => __( 'Search Services', '100mints' ),
		'parent_item_colon'  => __( 'Parent Services:', '100mints' ),
		'not_found'          => __( 'No services found.', '100mints' ),
		'not_found_in_trash' => __( 'No services found in Trash.', '100mints' )
	);

	$args = array(
		'labels'             => $labels,
		'description'        => __( 'Services offered', '100mints' ),
		'public'             => true,
		'publicly_queryable' => true,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'query_var'          => true,
		'rewrite'            => array( 'slug' => 'services' ),
		'capability_type'    => 'post',
		'has_archive'        => true,
		'hierarchical'       => false,
		'menu_position'      => 5,
		'menu_icon'          => 'dashicons-admin-tools',
		'supports'           => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments' ),
		'show_in_rest'       => true, // Enable Gutenberg editor
		'rest_base'          => 'services', // REST API base
	);

	register_post_type( 'services', $args );
}
add_action( 'init', 'register_services_post_type' );

// Register Services Type Taxonomy
function register_services_type_taxonomy() {
	$labels = array(
		'name'              => _x( 'Services Types', 'taxonomy general name', '100mints' ),
		'singular_name'     => _x( 'Services Type', 'taxonomy singular name', '100mints' ),
		'search_items'      => __( 'Search Services Types', '100mints' ),
		'all_items'         => __( 'All Services Types', '100mints' ),
		'parent_item'       => __( 'Parent Services Type', '100mints' ),
		'parent_item_colon' => __( 'Parent Services Type:', '100mints' ),
		'edit_item'         => __( 'Edit Services Type', '100mints' ),
		'update_item'       => __( 'Update Services Type', '100mints' ),
		'add_new_item'      => __( 'Add New Services Type', '100mints' ),
		'new_item_name'     => __( 'New Services Type Name', '100mints' ),
		'menu_name'         => __( 'Services Types', '100mints' ),
	);

	$args = array(
		'hierarchical'      => true,
		'labels'            => $labels,
		'show_ui'           => true,
		'show_admin_column' => true,
		'query_var'         => true,
		'rewrite'           => array( 'slug' => 'services-type' ),
		'show_in_rest'      => true, // Enable in REST API
	);

	register_taxonomy( 'services_type', array( 'services' ), $args );
}
add_action( 'init', 'register_services_type_taxonomy' );

// Register Clients Custom Post Type
function register_clients_post_type() {
    $labels = array(
        'name'               => _x( 'Clients', 'post type general name', '100mints' ),
        'singular_name'      => _x( 'Client', 'post type singular name', '100mints' ),
        'menu_name'          => _x( 'Clients', 'admin menu', '100mints' ),
        'name_admin_bar'     => _x( 'Client', 'add new on admin bar', '100mints' ),
        'add_new'            => _x( 'Add New', 'client', '100mints' ),
        'add_new_item'       => __( 'Add New Client', '100mints' ),
        'new_item'           => __( 'New Client', '100mints' ),
        'edit_item'          => __( 'Edit Client', '100mints' ),
        'all_items'          => __( 'All Clients', '100mints' ),
        'search_items'       => __( 'Search Clients', '100mints' ),
        'not_found'          => __( 'No clients found.', '100mints' ),
        'not_found_in_trash' => __( 'No clients found in Trash.', '100mints' )
    );

    $args = array(
        'labels'             => $labels,
        'description'        => __( 'Clients of the business', '100mints' ),
        'public'             => false,
        'publicly_queryable' => false,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => false,
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'menu_position'      => 6,
        'menu_icon'          => 'dashicons-groups',
        'supports'           => array( 'title', 'thumbnail' ),
        'show_in_rest'       => true
    );

    register_post_type( 'clients', $args );
}
add_action( 'init', 'register_clients_post_type' );











/**
 * Register Testimonials Custom Post Type
 */
function ten_mints_register_testimonials_post_type() {

    $labels = array(
        'name'                  => _x( 'Testimonials', 'Post Type General Name', 'text_domain' ),
        'singular_name'         => _x( 'Testimonial', 'Post Type Singular Name', 'text_domain' ),
        'menu_name'             => __( 'Testimonials', 'text_domain' ),
        'name_admin_bar'        => __( 'Testimonial', 'text_domain' ),
        'archives'              => __( 'Testimonial Archives', 'text_domain' ),
        'attributes'            => __( 'Testimonial Attributes', 'text_domain' ),
        'parent_item_colon'     => __( 'Parent Testimonial:', 'text_domain' ),
        'all_items'             => __( 'All Testimonials', 'text_domain' ),
        'add_new_item'          => __( 'Add New Testimonial', 'text_domain' ),
        'add_new'               => __( 'Add New', 'text_domain' ),
        'new_item'              => __( 'New Testimonial', 'text_domain' ),
        'edit_item'             => __( 'Edit Testimonial', 'text_domain' ),
        'update_item'           => __( 'Update Testimonial', 'text_domain' ),
        'view_item'             => __( 'View Testimonial', 'text_domain' ),
        'view_items'            => __( 'View Testimonials', 'text_domain' ),
        'search_items'          => __( 'Search Testimonial', 'text_domain' ),
        'not_found'             => __( 'Not found', 'text_domain' ),
        'not_found_in_trash'    => __( 'Not found in Trash', 'text_domain' ),
        'featured_image'        => __( 'Client Photo', 'text_domain' ),
        'set_featured_image'    => __( 'Set client photo', 'text_domain' ),
        'remove_featured_image' => __( 'Remove client photo', 'text_domain' ),
        'use_featured_image'    => __( 'Use as client photo', 'text_domain' ),
        'insert_into_item'      => __( 'Insert into testimonial', 'text_domain' ),
        'uploaded_to_this_item' => __( 'Uploaded to this testimonial', 'text_domain' ),
        'items_list'            => __( 'Testimonials list', 'text_domain' ),
        'items_list_navigation' => __( 'Testimonials list navigation', 'text_domain' ),
        'filter_items_list'     => __( 'Filter testimonials list', 'text_domain' ),
    );
    $args = array(
        'label'                 => __( 'Testimonial', 'text_domain' ),
        'description'           => __( 'Post Type for Client Testimonials', 'text_domain' ),
        'labels'                => $labels,
        'supports'              => array( 'title', 'editor', 'thumbnail' ), // Title = Name, Editor = Testimonial Text, Thumbnail = Client Photo
        'taxonomies'            => array(),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-testimonial',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => false,
        'exclude_from_search'   => true,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true, // Enable Gutenberg editor support
    );
    register_post_type( 'testimonials', $args );

}
add_action( 'init', 'ten_mints_register_testimonials_post_type', 0 );

/**
 * Add Meta Boxes for Job Title and Company
 */
function ten_mints_add_testimonial_meta_boxes() {
    add_meta_box(
        'testimonial_details',
        __( 'Testimonial Details', 'text_domain' ),
        'ten_mints_testimonial_details_meta_box_html',
        'testimonials',
        'normal', // context
        'high' // priority
    );
}
add_action( 'add_meta_boxes', 'ten_mints_add_testimonial_meta_boxes' );

/**
 * Meta Box HTML
 */
function ten_mints_testimonial_details_meta_box_html( $post ) {
    $job_title = get_post_meta( $post->ID, '_testimonial_job_title', true );
    $company = get_post_meta( $post->ID, '_testimonial_company', true );
    wp_nonce_field( 'ten_mints_testimonial_details_nonce_action', 'ten_mints_testimonial_details_nonce' );
    ?>
    <p>
        <label for="testimonial_job_title"><?php _e( 'Job Title', 'text_domain' ); ?></label><br>
        <input type="text" name="testimonial_job_title" id="testimonial_job_title" value="<?php echo esc_attr( $job_title ); ?>" class="widefat">
    </p>
    <p>
        <label for="testimonial_company"><?php _e( 'Company', 'text_domain' ); ?></label><br>
        <input type="text" name="testimonial_company" id="testimonial_company" value="<?php echo esc_attr( $company ); ?>" class="widefat">
    </p>
    <?php
}

/**
 * Save Meta Box Data
 */
function ten_mints_save_testimonial_meta_data( $post_id ) {
    // Check nonce
    if ( ! isset( $_POST['ten_mints_testimonial_details_nonce'] ) || ! wp_verify_nonce( $_POST['ten_mints_testimonial_details_nonce'], 'ten_mints_testimonial_details_nonce_action' ) ) {
        return;
    }

    // Check if user has permissions to save data
    if ( ! current_user_can( 'edit_post', $post_id ) ) {
        return;
    }

    // Check if not an autosave
    if ( wp_is_post_autosave( $post_id ) ) {
        return;
    }

    // Check if not a revision
    if ( wp_is_post_revision( $post_id ) ) {
        return;
    }

    // Sanitize and save Job Title
    if ( isset( $_POST['testimonial_job_title'] ) ) {
        $job_title = sanitize_text_field( $_POST['testimonial_job_title'] );
        update_post_meta( $post_id, '_testimonial_job_title', $job_title );
    } else {
        delete_post_meta( $post_id, '_testimonial_job_title' );
    }

    // Sanitize and save Company
    if ( isset( $_POST['testimonial_company'] ) ) {
        $company = sanitize_text_field( $_POST['testimonial_company'] );
        update_post_meta( $post_id, '_testimonial_company', $company );
    } else {
        delete_post_meta( $post_id, '_testimonial_company' );
    }
}
add_action( 'save_post_testimonials', 'ten_mints_save_testimonial_meta_data' );

/**
 * Register Solutions Custom Post Type
 */
function register_solutions_post_type() {
    $labels = array(
        'name'                  => _x( 'Solutions', 'Post Type General Name', '100mints' ),
        'singular_name'         => _x( 'Solution', 'Post Type Singular Name', '100mints' ),
        'menu_name'             => __( 'Solutions', '100mints' ),
        'name_admin_bar'        => __( 'Solution', '100mints' ),
        'archives'              => __( 'Solution Archives', '100mints' ),
        'attributes'            => __( 'Solution Attributes', '100mints' ),
        'parent_item_colon'     => __( 'Parent Solution:', '100mints' ),
        'all_items'             => __( 'All Solutions', '100mints' ),
        'add_new_item'          => __( 'Add New Solution', '100mints' ),
        'add_new'               => __( 'Add New', '100mints' ),
        'new_item'              => __( 'New Solution', '100mints' ),
        'edit_item'             => __( 'Edit Solution', '100mints' ),
        'update_item'           => __( 'Update Solution', '100mints' ),
        'view_item'             => __( 'View Solution', '100mints' ),
        'view_items'            => __( 'View Solutions', '100mints' ),
        'search_items'          => __( 'Search Solutions', '100mints' ),
        'not_found'             => __( 'No solutions found', '100mints' ),
        'not_found_in_trash'    => __( 'No solutions found in Trash', '100mints' ),
        'featured_image'        => __( 'Solution Image', '100mints' ),
        'set_featured_image'    => __( 'Set solution image', '100mints' ),
        'remove_featured_image' => __( 'Remove solution image', '100mints' ),
        'use_featured_image'    => __( 'Use as solution image', '100mints' ),
        'insert_into_item'      => __( 'Insert into solution', '100mints' ),
        'uploaded_to_this_item' => __( 'Uploaded to this solution', '100mints' ),
        'items_list'            => __( 'Solutions list', '100mints' ),
        'items_list_navigation' => __( 'Solutions list navigation', '100mints' ),
        'filter_items_list'     => __( 'Filter solutions list', '100mints' ),
    );

    $args = array(
        'label'                 => __( 'Solution', '100mints' ),
        'description'           => __( 'Business solutions and offerings', '100mints' ),
        'labels'                => $labels,
        'supports'              => array( 'title', 'editor', 'thumbnail', 'excerpt', 'author', 'revisions', 'page-attributes' ),
        'taxonomies'            => array( 'solution_category', 'solution_tag' ),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 7,
        'menu_icon'             => 'dashicons-lightbulb',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true, // Enable Gutenberg editor
        'rest_base'             => 'solutions', // REST API base
        'rewrite'               => array(
            'slug' => 'solutions',
            'with_front' => false
        ),
    );

    register_post_type( 'solutions', $args );
}
add_action( 'init', 'register_solutions_post_type' );


/**
 * Register Solution Tags Taxonomy
 */
function register_solution_tags_taxonomy() {
    $labels = array(
        'name'                       => _x( 'Solution Tags', 'Taxonomy General Name', '100mints' ),
        'singular_name'              => _x( 'Solution Tag', 'Taxonomy Singular Name', '100mints' ),
        'menu_name'                  => __( 'Tags', '100mints' ),
        'all_items'                  => __( 'All Tags', '100mints' ),
        'parent_item'                => __( 'Parent Tag', '100mints' ),
        'parent_item_colon'          => __( 'Parent Tag:', '100mints' ),
        'new_item_name'              => __( 'New Tag Name', '100mints' ),
        'add_new_item'               => __( 'Add New Tag', '100mints' ),
        'edit_item'                  => __( 'Edit Tag', '100mints' ),
        'update_item'                => __( 'Update Tag', '100mints' ),
        'view_item'                  => __( 'View Tag', '100mints' ),
        'separate_items_with_commas' => __( 'Separate tags with commas', '100mints' ),
        'add_or_remove_items'        => __( 'Add or remove tags', '100mints' ),
        'choose_from_most_used'      => __( 'Choose from the most used', '100mints' ),
        'popular_items'              => __( 'Popular Tags', '100mints' ),
        'search_items'               => __( 'Search Tags', '100mints' ),
        'not_found'                  => __( 'Not Found', '100mints' ),
        'no_terms'                   => __( 'No tags', '100mints' ),
        'items_list'                 => __( 'Tags list', '100mints' ),
        'items_list_navigation'      => __( 'Tags list navigation', '100mints' ),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array( 'slug' => 'solution-tag' ),
    );

    register_taxonomy( 'solution_tag', array( 'solutions' ), $args );
}
add_action( 'init', 'register_solution_tags_taxonomy' );


/**
 * Flush rewrite rules on theme activation to ensure custom post types work
 */
function solutions_flush_rewrite_rules() {
	ten_mints_register_testimonials_post_type();
    register_solutions_post_type();

    flush_rewrite_rules();
}
register_activation_hook( __FILE__, 'solutions_flush_rewrite_rules' );




