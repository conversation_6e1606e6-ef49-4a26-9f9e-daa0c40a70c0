/*
Theme Name: 100Mints
Theme URI: https://www.linkedin.com/in/abdu<PERSON>-gamal-207690133/
Author: <PERSON>
 Description: 100Mints - Website Theme Design  
Version: 1
Author URI: https://www.linkedin.com/in/abdullah-gamal-207690133/
Template: kadence
Text Domain: kadence-child
*/

/*  Custom Css By <PERSON>
- - - - - - - - - - - - - - - - - - - - */

.entry.loop-entry {
	    -webkit-transition: all .25s cubic-bezier(.02,.01,.47,1);
    transition: all .25s cubic-bezier(.02,.01,.47,1);
}


/* .entry.loop-entry:hover {
	webkit-transform: translateY(-5px);
    transform: translateY(-5px);
    -webkit-transition: all .25s cubic-bezier(.02,.01,.47,1);
    transition: all .25s cubic-bezier(.02,.01,.47,1);
	box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
    -webkit-box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
    -moz-box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
}

.kt-blocks-post-grid-item:hover {
	webkit-transform: translateY(-5px);
    transform: translateY(-5px);
    -webkit-transition: all .25s cubic-bezier(.02,.01,.47,1);
    transition: all .25s cubic-bezier(.02,.01,.47,1);
	box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
    -webkit-box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
    -moz-box-shadow: 0px 15px 15px -10px rgb(178 150 150 / 37%);;
} */



.social-icon-custom-svg svg *{
	fill: #000;
}

.kt-blocks-post-grid-item .entry-title a:not(.button):not(.list) {
    margin: 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}




#masthead .kadence-sticky-header.item-is-fixed:not(.item-at-start):not(.site-header-row-container):not(.item-hidden-above):not(.site-main-header-wrap), #masthead .kadence-sticky-header.item-is-fixed:not(.item-at-start):not(.item-hidden-above) > .site-header-row-container-inner {
    backdrop-filter: blur(9px);
    -webkit-backdrop-filter: blur(9px);
	box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px 0px;
}













.wp-block-kadence-advancedbtn .kt-btn-inner-text,
.site-middle-footer-inner-wrap .widget-area .widget-title {
    position: relative;
	padding-inline-start: 25px;
}

.wp-block-kadence-advancedbtn .kt-btn-inner-text::before {
    content: '';
    position: absolute;
    bottom: 9px;
    left: -10px;
    width: 24px;
    height: 6px;
    background-color: #fff;
    transition: transform 1s ease;
    opacity: 0.8;
    border-radius: 4px;
}

.wp-block-kadence-advancedbtn .kb-button:hover .kt-btn-inner-text::before {
    opacity: 1;
    transform: rotate(360deg);
}




.white-btn .kt-btn-inner-text::before {
    background-color: var(--global-palette1);

}



.footer-social-wrap  .widget-title {
	padding-inline-start: 0!important;
}



.footer-social-wrap  .widget-title::before {
	content: '';
    position: relative;
	display: none;
    bottom: 0px;
    left: 0px;
    width: 0px;
    height: 0px;
    background-color: transparent;
    transition: transform 1s ease;
    opacity: 0.8;
    border-radius: 4px;
}



.site-middle-footer-inner-wrap .widget-area .widget-title::before {
    content: '';
    position: absolute;
    bottom: 10px;
    left: -15px;
    width: 30px;
    height: 10px;
    background-color: var(--global-palette2);
    transition: transform 1s ease;
    opacity: 0.8;
    border-radius: 4px;
}



.item-is-stuck .widget-toggle-icon svg g > path:first-child {
	stroke: #08074b!important;
}














.animated_image {
	animation: smooth-float 5s ease-in-out infinite;
	will-change: transform; 
	transform-origin: center center;
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
	-webkit-font-smoothing: subpixel-antialiased;
	max-width: 100%;
	height: auto;
	display: inline-block;
  }
  
  @keyframes smooth-float {
	0% {
	  transform: translateY(0%) translateX(0%);
	}
	25% {
	  transform: translateY(-3%) translateX(2%); /* Using percentages of element size */
	}
	50% {
	  transform: translateY(0%) translateX(4%);
	}
	75% {
	  transform: translateY(3%) translateX(1%);
	}
	100% {
	  transform: translateY(0%) translateX(0%);
	}
  }
  @media (max-width: 576px) {
	.animated_image {
	  animation-duration: 3s; 
	}
	
	@keyframes smooth-float {
	  0% {
		transform: translateY(0%) translateX(0%);
	  }
	  25% {
		transform: translateY(-2%) translateX(1.5%); 
	  }
	  50% {
		transform: translateY(0%) translateX(3%);
	  }
	  75% {
		transform: translateY(2%) translateX(1%);
	  }
	  100% {
		transform: translateY(0%) translateX(0%);
	  }
	}
  }
  
